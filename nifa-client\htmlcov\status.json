{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "f9f0b06f61e8b0d035eab75b66ff97a3", "files": {"z_9589fface2250470___init___py": {"hash": "f60926621cadaf715e3ace010e761c54", "index": {"url": "z_9589fface2250470___init___py.html", "file": "nifa\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838___init___py": {"hash": "8d7d2a40d0e09174b007471272e0bda4", "index": {"url": "z_aee3eced65d61838___init___py.html", "file": "nifa\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_client_py": {"hash": "eeca72284d8c65bc81ac51e451f64585", "index": {"url": "z_aee3eced65d61838_client_py.html", "file": "nifa\\api\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_data_py": {"hash": "2e17597d315eb6bdbe7e1e3eadb28eca", "index": {"url": "z_aee3eced65d61838_data_py.html", "file": "nifa\\api\\data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_info_py": {"hash": "5e82da48a33729002c1e8552c13b2223", "index": {"url": "z_aee3eced65d61838_info_py.html", "file": "nifa\\api\\info.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_judicial_py": {"hash": "d30db84da9e094d5eb6b213d46b3cd39", "index": {"url": "z_aee3eced65d61838_judicial_py.html", "file": "nifa\\api\\judicial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_query_count_py": {"hash": "4108181f565ed385b74c8313051e4976", "index": {"url": "z_aee3eced65d61838_query_count_py.html", "file": "nifa\\api\\query_count.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_task_py": {"hash": "47df139e3ccf95b9034972a0002fdf57", "index": {"url": "z_aee3eced65d61838_task_py.html", "file": "nifa\\api\\task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 65, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2___init___py": {"hash": "54aea83e2efc212ad13a0243fad759a0", "index": {"url": "z_e30577f822d2f7f2___init___py.html", "file": "nifa\\auth\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2_encryption_py": {"hash": "48042374cf913bec48c08f0662094af7", "index": {"url": "z_e30577f822d2f7f2_encryption_py.html", "file": "nifa\\auth\\encryption.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2_signature_py": {"hash": "d8002daf531f8b4b2bdcaad4a9d05612", "index": {"url": "z_e30577f822d2f7f2_signature_py.html", "file": "nifa\\auth\\signature.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f8c89c1e0a6a595___init___py": {"hash": "a54cb97746e044883578ba8ab143ce9d", "index": {"url": "z_9f8c89c1e0a6a595___init___py.html", "file": "nifa\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f8c89c1e0a6a595_settings_py": {"hash": "ccbe01e67fe63c4bb8f2f54a77855ec8", "index": {"url": "z_9f8c89c1e0a6a595_settings_py.html", "file": "nifa\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 47, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2cbf57cf94805440___init___py": {"hash": "5261b8ec2eb0e9ba3b76f1df7fe689ed", "index": {"url": "z_2cbf57cf94805440___init___py.html", "file": "nifa\\exceptions\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2cbf57cf94805440_base_py": {"hash": "0b788e134385a5b2b839766d986ad728", "index": {"url": "z_2cbf57cf94805440_base_py.html", "file": "nifa\\exceptions\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 58, "n_excluded": 0, "n_missing": 33, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe___init___py": {"hash": "0b6633963b2a0b0b44dc735b8662bc64", "index": {"url": "z_1b14f832205fdefe___init___py.html", "file": "nifa\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe_helpers_py": {"hash": "04a1644d589718127500ed1bd76f9888", "index": {"url": "z_1b14f832205fdefe_helpers_py.html", "file": "nifa\\utils\\helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 75, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe_validators_py": {"hash": "0babf417af15131055b84fda2f842f33", "index": {"url": "z_1b14f832205fdefe_validators_py.html", "file": "nifa\\utils\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 67, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}