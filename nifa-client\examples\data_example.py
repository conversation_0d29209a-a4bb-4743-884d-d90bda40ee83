"""
数据上报示例
演示如何使用DataAPI进行数据上报和文件上传
"""

import logging
import os
import tempfile
from nifa.api.data import DataAPI
from nifa.exceptions.base import NifaError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    # 设置环境变量
    os.environ["NIFA_ORG_CODE"] = "YOUR_ORG_CODE"
    os.environ["NIFA_BASE_URL"] = "https://api.nifa.org.cn"
    # 如果需要加密，设置SM4密钥
    # os.environ["NIFA_SM4_KEY"] = "YOUR_SM4_KEY"
    
    # 创建API客户端
    data_api = DataAPI()
    
    try:
        # 示例1: 上报单条数据
        logger.info("=== 单条数据上报示例 ===")
        single_data = {
            "customerName": "张三",
            "idCard": "110101199001011234",
            "loanAmount": "100000.00",
            "loanDate": "2023-01-01",
            "status": "正常"
        }
        
        upload_result = data_api.upload_data(
            data_type="loan_info",
            data_content=single_data,
            batch_no="BATCH20231201001",
            encrypt=False  # 是否加密
        )
        logger.info(f"单条数据上报结果: {upload_result}")
        
        # 示例2: 上报JSON格式数据
        logger.info("=== JSON数据上报示例 ===")
        json_data = [
            {
                "customerName": "李四",
                "idCard": "110101199001011235",
                "loanAmount": "200000.00",
                "loanDate": "2023-01-02",
                "status": "正常"
            },
            {
                "customerName": "王五",
                "idCard": "110101199001011236",
                "loanAmount": "150000.00",
                "loanDate": "2023-01-03",
                "status": "逾期"
            }
        ]
        
        json_upload_result = data_api.upload_data(
            data_type="loan_batch",
            data_content=json_data,
            batch_no="BATCH20231201002"
        )
        logger.info(f"JSON数据上报结果: {json_upload_result}")
        
        # 示例3: 批量数据上报
        logger.info("=== 批量数据上报示例 ===")
        batch_data_list = [
            {
                "dataType": "customer_info",
                "dataContent": {
                    "customerName": "赵六",
                    "idCard": "110101199001011237",
                    "phone": "13812345678",
                    "address": "北京市朝阳区"
                }
            },
            {
                "dataType": "loan_info",
                "dataContent": {
                    "customerName": "赵六",
                    "idCard": "110101199001011237",
                    "loanAmount": "300000.00",
                    "loanDate": "2023-01-04"
                }
            }
        ]
        
        batch_upload_result = data_api.batch_upload_data(
            data_list=batch_data_list,
            batch_no="BATCH20231201003",
            encrypt=False
        )
        logger.info(f"批量数据上报结果: {batch_upload_result}")
        
        # 示例4: 文件上传
        logger.info("=== 文件上传示例 ===")
        
        # 创建临时文件用于演示
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as temp_file:
            temp_file.write("姓名,身份证号,贷款金额,贷款日期\n")
            temp_file.write("张三,110101199001011234,100000.00,2023-01-01\n")
            temp_file.write("李四,110101199001011235,200000.00,2023-01-02\n")
            temp_file_path = temp_file.name
        
        try:
            file_upload_result = data_api.upload_file(
                file_path=temp_file_path,
                file_type="csv",
                description="贷款数据文件",
                encrypt=False
            )
            logger.info(f"文件上传结果: {file_upload_result}")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
        
        # 示例5: 查询上报状态
        logger.info("=== 上报状态查询示例 ===")
        if upload_result.get("data", {}).get("taskId"):
            task_id = upload_result["data"]["taskId"]
            status_result = data_api.get_upload_status(task_id=task_id)
            logger.info(f"上报状态查询结果: {status_result}")
        
    except NifaError as e:
        logger.error(f"NIFA API错误: {e}")
        logger.error(f"错误代码: {e.error_code}")
        logger.error(f"错误详情: {e.details}")
        
    except Exception as e:
        logger.error(f"未知错误: {e}")
        
    finally:
        # 关闭API客户端
        data_api.close()
        logger.info("API客户端已关闭")


def encrypted_upload_example():
    """加密上报示例"""
    logger.info("=== 加密数据上报示例 ===")
    
    # 注意：需要先配置SM4密钥
    os.environ["NIFA_SM4_KEY"] = "YOUR_32_CHAR_HEX_SM4_KEY"
    
    try:
        with DataAPI() as data_api:
            sensitive_data = {
                "customerName": "敏感客户",
                "idCard": "110101199001011238",
                "bankAccount": "6222021234567890123",
                "loanAmount": "500000.00"
            }
            
            # 加密上报
            result = data_api.upload_data(
                data_type="sensitive_loan_info",
                data_content=sensitive_data,
                encrypt=True  # 启用加密
            )
            logger.info(f"加密上报结果: {result}")
            
    except NifaError as e:
        logger.error(f"加密上报失败: {e}")


def large_file_upload_example():
    """大文件上传示例"""
    logger.info("=== 大文件上传示例 ===")
    
    try:
        with DataAPI() as data_api:
            # 创建一个较大的测试文件
            large_file_path = "large_data.txt"
            
            with open(large_file_path, 'w', encoding='utf-8') as f:
                for i in range(10000):
                    f.write(f"数据行 {i}: 这是第{i}行测试数据\n")
            
            try:
                result = data_api.upload_file(
                    file_path=large_file_path,
                    file_type="txt",
                    description="大数据文件测试"
                )
                logger.info(f"大文件上传结果: {result}")
                
            finally:
                # 清理文件
                if os.path.exists(large_file_path):
                    os.unlink(large_file_path)
                    
    except NifaError as e:
        logger.error(f"大文件上传失败: {e}")


def error_handling_example():
    """错误处理示例"""
    logger.info("=== 错误处理示例 ===")
    
    data_api = DataAPI()
    
    try:
        # 故意上报空数据来演示错误处理
        data_api.upload_data(
            data_type="",  # 空数据类型
            data_content=None  # 空内容
        )
        
    except NifaError as e:
        logger.error(f"验证错误: {e}")
        logger.error(f"错误字段: {e.details.get('field')}")
        logger.error(f"错误值: {e.details.get('value')}")
        
    finally:
        data_api.close()


if __name__ == "__main__":
    # 运行主示例
    main()
    
    # 运行其他示例
    # encrypted_upload_example()  # 需要配置SM4密钥
    # large_file_upload_example()
    error_handling_example()
