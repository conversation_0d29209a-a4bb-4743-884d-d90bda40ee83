package cn.org.nifa.credit;

import static org.junit.Assert.assertEquals;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;
import org.codehaus.jettison.json.JSONObject;
import org.junit.Test;

import cn.org.nifa.bxcredit.Utils;

public class UtilsTest {
	@Test
	public void testGetRandomNumber() {
		System.out.println(Utils.getRandomNumber(10));
		for(int i=0; i<100; i++) {
			assertEquals(i, Utils.getRandomNumber(i).length());
		}
	}
	@Test
	public void testMap2Json() {
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sname", "张三");  
		headerMap.put("stype", "0");  
		headerMap.put("sreason", "a");  
		headerMap.put("sno", "362201198607112613");
		
		JSONObject json = Utils.map2Json(headerMap);
		System.out.println(json.toString());
	}
	@Test
	public void testEncryptFile() {
		File file = Utils.encryptFile("tmp/MA05M6KK9201810221201120001.zip", "tmp/MA05M6KK9201810221201120001.enc");
		System.out.println(file);
	}
	@Test
	public void testMd5() {
		String md5 = Utils.string2Md5("asfd");
		System.out.println("md5=" + md5);
		assertEquals(md5, "d78c03d72e72b44a131d255aec3c8a11");
	}
	@Test
	public void test() {
		String string = "BMhBZtqHsZKvofCWpWAAPbcyXm5UCbPhreXlnGeI20FjUsVydSsijMaqm+Y2ab0Bq4lPJIW4U+TdoKZblI1qzhmspr0TLlM2/mUfgsYlaj4i6xzTz3rQ55kotLLo/Zj4VLkmzkBVZW/vxPnal2vDuHEpo7fr8AkuTfclVM5UlXFtPy7OWlXP8BYcsvso1pZh5fkuw7f6aVeb1mncOIvd7TsOQa7DnT89qkSKAZh32RrJ0fkRvIY35F7m316SvZJLVfOqcpaM9KOgDK4Joqi/aTSc7xkdhw+soZMWJI3nBg==";
		File decEncFile = new File("tmp/test.enc");//生成enc解密文件
		try {
			FileUtils.writeByteArrayToFile(decEncFile,Base64.decodeBase64(string));
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		Utils.deccryptFile(decEncFile.getPath(), "tmp/test.txt");
	}
	@Test
	public void testSha() {
		String preparedSign = "1234";
		System.out.println(Utils.sha256(preparedSign));
	}
}
