import sys

if sys.platform == "win32":
    # Actually defined here and re-exported from os at runtime,
    # but this leads to less code duplication
    from os import (
        F_OK as F_OK,
        O_APPEND as O_APPEND,
        O_BINARY as O_BINARY,
        O_CREAT as O_CREA<PERSON>,
        <PERSON>_<PERSON>XCL as <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>_<PERSON>INH<PERSON><PERSON> as <PERSON>_<PERSON>INH<PERSON><PERSON>,
        O_RANDOM as O_RAND<PERSON>,
        O_RDONL<PERSON> as O_RDONLY,
        O_RD<PERSON> as O_RD<PERSON>,
        O_SEQUENTIAL as O_SEQUENTIAL,
        O_SHORT_LIVED as O_SHORT_LIVED,
        O_TEMPORARY as O_TEMPORARY,
        O_TEXT as O_TEXT,
        O_TRUNC as O_TRUN<PERSON>,
        O_WRONLY as O_WRONLY,
        P_DETACH as P_<PERSON><PERSON><PERSON>,
        P_<PERSON><PERSON><PERSON> as P_<PERSON>WA<PERSON>,
        P_<PERSON><PERSON>ITO as P_NOWAITO,
        P_OVERLAY as P_OVERLAY,
        P_WAIT as P_<PERSON><PERSON>,
        R_<PERSON> as R_<PERSON>,
        TMP_<PERSON>X as TMP_<PERSON><PERSON>,
        W_OK as W_<PERSON>,
        <PERSON>_<PERSON> as <PERSON>_<PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
        abort as abort,
        access as access,
        chdir as chdir,
        chmod as chmod,
        close as close,
        closerange as closerange,
        cpu_count as cpu_count,
        device_encoding as device_encoding,
        dup as dup,
        dup2 as dup2,
        error as error,
        execv as execv,
        execve as execve,
        fspath as fspath,
        fstat as fstat,
        fsync as fsync,
        ftruncate as ftruncate,
        get_handle_inheritable as get_handle_inheritable,
        get_inheritable as get_inheritable,
        get_terminal_size as get_terminal_size,
        getcwd as getcwd,
        getcwdb as getcwdb,
        getlogin as getlogin,
        getpid as getpid,
        getppid as getppid,
        isatty as isatty,
        kill as kill,
        link as link,
        listdir as listdir,
        lseek as lseek,
        lstat as lstat,
        mkdir as mkdir,
        open as open,
        pipe as pipe,
        putenv as putenv,
        read as read,
        readlink as readlink,
        remove as remove,
        rename as rename,
        replace as replace,
        rmdir as rmdir,
        scandir as scandir,
        set_handle_inheritable as set_handle_inheritable,
        set_inheritable as set_inheritable,
        spawnv as spawnv,
        spawnve as spawnve,
        startfile as startfile,
        stat as stat,
        stat_result as stat_result,
        statvfs_result as statvfs_result,
        strerror as strerror,
        symlink as symlink,
        system as system,
        terminal_size as terminal_size,
        times as times,
        times_result as times_result,
        truncate as truncate,
        umask as umask,
        uname_result as uname_result,
        unlink as unlink,
        urandom as urandom,
        utime as utime,
        waitpid as waitpid,
        write as write,
    )

    if sys.version_info >= (3, 9):
        from os import unsetenv as unsetenv, waitstatus_to_exitcode as waitstatus_to_exitcode
    if sys.version_info >= (3, 11):
        from os import EX_OK as EX_OK
    if sys.version_info >= (3, 12):
        from os import (
            get_blocking as get_blocking,
            listdrives as listdrives,
            listmounts as listmounts,
            listvolumes as listvolumes,
            set_blocking as set_blocking,
        )
    if sys.version_info >= (3, 13):
        from os import fchmod as fchmod, lchmod as lchmod

    environ: dict[str, str]
