package cn.org.nifa.bxcredit;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.math.ec.ECPoint;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;

import com.cfcc.jaf.crypto.CryptoUtil;
import com.cfcc.jaf.crypto.sm.SMUtil;

public class Utils {
	/**
	 * 获取指定长度的随机数
	 * @param length
	 * @return
	 */
	
	public static String getRandomNumber(int length) {
		String result = "";
		Random rnd = new Random();
		for(int i=0; i<length; i++) {
			result += rnd.nextInt(10);
		}
		return result;
	}
	/**
	 * sha256摘要算法
	 * @param string
	 * @return
	 */
	public static String sha256(String string) {
		String result = null;
        try {
        	MessageDigest sha = MessageDigest.getInstance("SHA-256");
			sha.update(string.getBytes("UTF-8"));
			byte b[] = sha.digest();
			int i;
			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				String temp = Integer.toHexString(i&0xFF);
				if (temp.length() == 1) {
					buf.append("0");
				}
				buf.append(temp);
			}
			result = buf.toString();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return result;
	}
	/**
	 * 字符串转md5
	 * @param string
	 * @return
	 */
	public static String string2Md5(String string) {
        {
            try {
                MessageDigest md5 = MessageDigest.getInstance("MD5");
                md5.update(string.getBytes("UTF-8"));
                byte[] encryption = md5.digest();
 
                StringBuffer strBuf = new StringBuffer();
                for (int i = 0; i < encryption.length; i++) {
                    if (Integer.toHexString(0xff & encryption[i]).length() == 1) {
                        strBuf.append("0").append(Integer.toHexString(0xff & encryption[i]));
                    } else {
                        strBuf.append(Integer.toHexString(0xff & encryption[i]));
                    }
                }
 
                return strBuf.toString();
            } catch (NoSuchAlgorithmException e) {
                return "";
            } catch (UnsupportedEncodingException e) {
                return "";
            }
        }
    }
	/**
	 * map to json
	 * 
	 * @param map
	 * @return
	 */
	public static JSONObject map2Json(Map<String, String> map){
		JSONObject json = new JSONObject();
		try {
			// https://www.cnblogs.com/imzhj/p/5981665.html
			for (Entry<String, String> entry : map.entrySet()) {
				json.put(entry.getKey(), entry.getValue());
			} 
		} catch (JSONException e) {
			e.printStackTrace();
		}
		return json;
	}
	/**
	 * 国密加密 zip转enc
	 * @param sourceFile
	 * @param newFile
	 * @return
	 */
	public static File encryptFile(String sourceFile,String newFile){
		File file2 = null;
		try{
			byte[] pubx = CryptoUtil.toByteArray(Config.PUB_X_KEY);
			// 公钥2
			byte[] puby = CryptoUtil.toByteArray(Config.PUB_Y_KEY);
			ECPoint pubKey = SMUtil.createECPoint(pubx, puby);
			//需要加密文件
			File file =new File(sourceFile);
			//把文件变成直接数组
			byte [] b=CryptoUtil.readFile(file);
			//加密 返回密文数组
			byte [] b2=SMUtil.encryptBySM2(pubKey, b);
			//加密写成.enc文件
			file2=new File(newFile);
			if (!file2.exists()) {
				file2.createNewFile();
			}
			CryptoUtil.writeFile(b2,file2);
		}catch(Exception e){
			e.printStackTrace();
		}
		return file2;
	}
	/**
	 * 国密解密 env转zip
	 * @param sourceFile
	 * @param newFile
	 * @return
	 */
	public static File deccryptFile(String sourceFile,String newFile){//解密
		File file3 = null;
		try{
			byte[] prvKey = CryptoUtil.toByteArray(Config.PRV_KEY);
			//加密写成.enc文件
			File file4=new File(sourceFile);
			//读取需要解密文件
			byte [] prb=CryptoUtil.readFile(file4);
			//解密 返回明文数组
			byte [] prb2=SMUtil.decryptBySM2(prvKey, prb);
			file3=new File(newFile);
			if (!file3.getParentFile().exists()) {
//            	boolean mkdirs = file3.getParentFile().mkdirs();
            	file3.getParentFile().mkdirs();
			}
			if (!file3.exists()) {
//				boolean createNewFile = file3.createNewFile();
				file3.createNewFile();
			}
			//解密写成.zip文件
			CryptoUtil.writeFile(prb2, file3);
		}catch(Exception e){
			e.printStackTrace();
		}
		return file3;
	}
	/**
	 * 读取file 转为base64字符串
	 * @param file
	 * @return
	 */
	public static String file2Base64String(File file) {
		String encodeBase64String = null;
		try {
			FileInputStream instream = new FileInputStream(file);
			byte[] buffer = new byte[(int) file.length()];
			instream.read(buffer);
			instream.close();
			encodeBase64String = Base64.encodeBase64String(buffer);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return encodeBase64String;
	}

	public static void main(String[] args) {
		String filename = "097967874202209051715127011";
		String zipFilename = "tmp/"+filename+".zip";
		System.out.println(zipFilename);
//		// zip通过国密算法加密为enc文件
		String encFilename = "tmp/"+filename+".enc";
		System.out.println(encFilename);

		File file = Utils.deccryptFile(encFilename,zipFilename );
		// 读取enc文件为字符串
		String base64EncString = Utils.file2Base64String(file);
		System.out.println(base64EncString);

		String val = "0979678748938538467a000775eada4dd4e536d132feaeb04800ef9053971ff23f0e9e76a448de825e34bmNak7HBrKFFXMn72w5";
		System.out.println("sign:" + Utils.sha256(val));
		System.out.println("sign_info: a93c096700c1b5c303f13c21c9be3b0b10c3e75f1b053e1b8f1e99db6912e4f5 " );
		System.out.println("sign_info: a93c096700c1b5c303f13c21c9be3b0b10c3e75f1b053e1b8f1e99db6912e4f5 " );

	}
	

}
