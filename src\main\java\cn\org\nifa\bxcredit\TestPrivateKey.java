package cn.org.nifa.bxcredit;

import java.io.File;
import java.io.IOException;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;

public class TestPrivateKey {
	

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		File decEncFile = new File("D:\\infoshare2\\00CWC00122014050510143200641.enc"); //解密enc文件
	
		Utils.deccryptFile(decEncFile.getPath(), "D:\\infoshare2\\00CWC001220140505101432006411.txt"); //解密结果。里面的错误代码从《采集标准》查询
	}

}
