package cn.org.nifa.bxcredit;

import java.net.Proxy;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Map.Entry;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.core.MediaType;

import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.WebResource.Builder;
import com.sun.jersey.api.client.config.ClientConfig;
import com.sun.jersey.api.client.config.DefaultClientConfig;

// 一个简单的jersey client封装
public class JerseyClient {
	// 接口地址
	String uri;
	// Client instances are expensive resources. It is recommended 
	// a configured instance is reused for the creation of Web resources
	Client client = null;
	ClientConfig clientConfig = null;
	WebResource resource = null;
	Proxy proxy;
	public JerseyClient(String uri) {
		try {
			this.uri = uri;
			// 处理SSL证书
			TrustManager[] trustManagers = new TrustManager[] { 
				new X509TrustManager() {
					public X509Certificate[] getAcceptedIssuers() {return null; }
					public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {}
					public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {}
				}
			};
			SSLContext context = SSLContext.getInstance("TLS");
			context.init(null, trustManagers, new SecureRandom());
			HttpsURLConnection.setDefaultSSLSocketFactory(context.getSocketFactory());
			
			this.clientConfig = new DefaultClientConfig();
			// 超时设定 查询接口建议设置10s 报送接口建议设置120s
			this.client = Client.create();
			// resource
			this.resource = this.client.resource(this.uri);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (KeyManagementException e) {
			e.printStackTrace();
		}
	}
	public ClientResponse post(Map<String, String> headerMap, 
			Map<String, String> bodyMap){
		ClientResponse response = null;
		String body = null;
		Builder resourceBuilder = this.resource
				.accept(MediaType.APPLICATION_JSON)
				.type(MediaType.APPLICATION_JSON);
//		.type(MediaType.APPLICATION_FORM_URLENCODED);
		// some header
		if(headerMap!= null && !headerMap.isEmpty()) {
			for (Entry<String, String> entry : headerMap.entrySet()) {
				resourceBuilder = resourceBuilder
						.header(entry.getKey(), entry.getValue());
			}
		}
		// body
		body = Utils.map2Json(bodyMap).toString();
		response = resourceBuilder.post(ClientResponse.class, body);
		return response;
	}
	
}
