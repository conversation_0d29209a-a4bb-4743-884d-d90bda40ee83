"""
配置管理模块
支持多环境配置和环境变量管理
"""

import os
from enum import Enum
from typing import Optional, Dict, Any
from pathlib import Path

from pydantic import Field
from pydantic_settings import BaseSettings


class Environment(str, Enum):
    """环境枚举"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class Settings(BaseSettings):
    """
    应用配置类
    支持从环境变量、.env文件和配置文件中读取配置
    """
    
    # 基础配置
    ENVIRONMENT: Environment = Field(
        default=Environment.DEVELOPMENT,
        description="运行环境"
    )
    
    DEBUG: bool = Field(
        default=True,
        description="调试模式"
    )
    
    # NIFA API配置
    NIFA_BASE_URL: str = Field(
        default="https://api.nifa.org.cn",
        description="NIFA API基础URL"
    )
    
    NIFA_ORG_CODE: Optional[str] = Field(
        default=None,
        description="机构代码"
    )
    
    NIFA_TIMEOUT: int = Field(
        default=30,
        description="请求超时时间（秒）"
    )
    
    NIFA_MAX_RETRIES: int = Field(
        default=3,
        description="最大重试次数"
    )
    
    NIFA_RETRY_DELAY: float = Field(
        default=1.0,
        description="重试延迟时间（秒）"
    )
    
    # 签名配置
    NIFA_SIGNATURE_ALGORITHM: str = Field(
        default="SHA256",
        description="签名算法"
    )
    
    # 加密配置
    NIFA_SM2_PUBLIC_KEY: Optional[str] = Field(
        default=None,
        description="SM2公钥"
    )
    
    NIFA_SM2_PRIVATE_KEY: Optional[str] = Field(
        default=None,
        description="SM2私钥"
    )
    
    NIFA_SM4_KEY: Optional[str] = Field(
        default=None,
        description="SM4密钥"
    )
    
    # 日志配置
    LOG_LEVEL: str = Field(
        default="INFO",
        description="日志级别"
    )
    
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # 缓存配置
    CACHE_ENABLED: bool = Field(
        default=False,
        description="是否启用缓存"
    )
    
    CACHE_TTL: int = Field(
        default=300,
        description="缓存过期时间（秒）"
    )
    
    # 连接池配置
    CONNECTION_POOL_SIZE: int = Field(
        default=10,
        description="连接池大小"
    )
    
    CONNECTION_POOL_MAX_SIZE: int = Field(
        default=20,
        description="连接池最大大小"
    )
    

    
    class Config:
        """Pydantic配置"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        validate_assignment = True
        
        # 环境变量前缀
        env_prefix = ""
        
        @classmethod
        def customise_sources(
            cls,
            init_settings,
            env_settings,
            file_secret_settings,
        ):
            """自定义配置源优先级"""
            return (
                init_settings,
                env_settings,
                file_secret_settings,
            )
    
    def get_api_url(self, endpoint: str) -> str:
        """获取完整的API URL"""
        return f"{self.NIFA_BASE_URL}/{endpoint.lstrip('/')}"
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == Environment.DEVELOPMENT
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == Environment.PRODUCTION
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "level": self.LOG_LEVEL,
            "format": self.LOG_FORMAT,
            "handlers": ["console"] if self.is_development() else ["file"],
        }


# 全局配置实例
settings = Settings()
