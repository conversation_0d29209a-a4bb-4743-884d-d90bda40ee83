package cn.org.nifa.bxcredit;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import com.sun.jersey.api.client.ClientResponse;

// [报送接口]
// 一个数据传送接口，调用成功说明数据包已经报送到协会系统
// 报送成功后进入入库阶段，数据入库状态查看，需要调用[TaskDemo.java]
public class DataDemo {
	private static Map<String, String> getHeaderMap(Map<String, String> map, 
			String filename) {
		// 
		String scode = Utils.string2Md5(map.get("sdata"));
		// 构造签名 用于身份验证
		// .append(sbankcode).append(sdatacode).append(scode).append("cfcc");
		String preparedSign = Config.ORGCODE //sbankcode
				+ filename //sdatacode
				+ scode //scode
				+ Config.KEY; //key
		String sign = Utils.sha256(preparedSign);
		sign = sign.trim().replace("\n", "").replace("\r","");
		// 构造header
		/*
		 * .header("sbankcode", sbankcode)
    					.header("sdatacode", sdatacode)
    					.header("scode", scode)
    					.header("sign",sign)
		 */
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sbankcode", Config.ORGCODE);
		headerMap.put("sdatacode", filename);
		headerMap.put("scode", scode);  
		headerMap.put("sign", sign);  
		return headerMap;
		
	}
	public static void main(String[] args) {
		// 构造client--耗费资源 适合多次使用
		JerseyClient client = new JerseyClient(Config.DATA_URI);
		ClientResponse resp = null;
		
		// 构造body
		// 制作zip文件 请自己写，此处用制作好的zip文件做演示

		String filename = "2RR3PEGT2202309061715127112";
		String zipFilename = "tmp/"+filename+".zip";
//		// zip通过国密算法加密为enc文件
		String encFilename = "tmp/"+filename+".enc";

		File file = Utils.encryptFile(zipFilename, encFilename);
		// 读取enc文件为字符串
		String base64EncString = Utils.file2Base64String(file);
		// 构造body
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("sdata", base64EncString);  
		System.out.println("[preparedMap]: \n" + Utils.map2Json(bodyMap));
		// 构造header
		Map<String, String> headerMap = getHeaderMap(bodyMap, filename);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp.getStatus() == 200) {
			System.out.println(resp.getEntity(String.class));
			

			 
		}else {
			System.out.println("[ERROR] Status: " + resp.getStatus());
			System.out.println(resp.getEntity(String.class));
		}
	}
}
