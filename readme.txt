﻿中国互联网金融协会
互联网金融信息共享平台（个人借贷业务、信用保证保险业务用）-接口DEMO
JAVA版


【文件夹说明】
lib/		本地jar包
src/		标准maven
target/		标准maven
tmp/		报送demo用的文件

【文件说明】
DataDemo.java 	[报送接口]demo
TaskDemo.java  	[报送状态查询接口]demo
InfoDemo.java  	[信用信息查询接口]demo
QueryCountDemo.java   [查询量耗用查询接口]demo
Config.java		参数配置文件，机构需根据自己情况修改
Utils.java		工具类
JerseyClient.java  	Jersey简单封装

【使用步骤】
运行环境：MAVEN、 jdk1.8、Eclipse
1.用Eclipse加载工程；
2.直接运行DataDemo.java、TaskDemo.java、InfoDemo.java里的3个main函数，没有报错说明网络无问题、工程编译正确；
3.协会提供开通接口的《回执》，机构依据回执信息修改Config.java参数类；
4.开始coding。

注意：
- 在做接口改造前务必先运行一下demo，保证demo能运行、无报错。
- demo没有对接口做充足的异常处理，请根据接口规范自行添加。
- 接口以及IP白名单的开通，机构可联系微信群中的协会客服了解流程。
- 联调测试环境的查询接口的调试过程中，可能遇到没有查询量的问题，机构可联系微信群中的协会客服解决。

【更新记录】
2019-04-11：解决body为空时请求异常的bug
2019-04-04：添加查询量耗用接口，支持信用保证保险业务，修复已知bug
2018-11-05：修复taskdemo中的debug
2018-10-23：完成最小化demo
