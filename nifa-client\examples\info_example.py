"""
信用信息查询示例
演示如何使用InfoAPI进行个人和企业信用信息查询
"""

import logging
import os
from nifa.api.info import InfoAPI
from nifa.exceptions.base import NifaError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    # 设置环境变量（实际使用时应该在.env文件中配置）
    os.environ["NIFA_ORG_CODE"] = "YOUR_ORG_CODE"
    os.environ["NIFA_BASE_URL"] = "https://api.nifa.org.cn"
    
    # 创建API客户端
    info_api = InfoAPI()
    
    try:
        # 示例1: 查询个人信用信息
        logger.info("=== 个人信用信息查询示例 ===")
        personal_result = info_api.query_personal_credit(
            id_card="110101199001011234",  # 示例身份证号
            name="张三",
            query_type="01",  # 基础信息查询
            query_reason="贷款申请信用查询"
        )
        logger.info(f"个人信用查询结果: {personal_result}")
        
        # 示例2: 查询企业信用信息
        logger.info("=== 企业信用信息查询示例 ===")
        enterprise_result = info_api.query_enterprise_credit(
            org_code="91110000123456789X",  # 示例统一社会信用代码
            org_name="示例企业有限公司",
            query_type="02",  # 详细信息查询
            query_reason="合作伙伴信用评估"
        )
        logger.info(f"企业信用查询结果: {enterprise_result}")
        
        # 示例3: 批量查询个人信用信息
        logger.info("=== 批量个人信用信息查询示例 ===")
        query_list = [
            {"idCard": "110101199001011234", "name": "张三"},
            {"idCard": "110101199001011235", "name": "李四"},
            {"idCard": "110101199001011236", "name": "王五"}
        ]
        
        batch_result = info_api.batch_query_personal_credit(
            query_list=query_list,
            query_type="01",
            query_reason="批量信用评估"
        )
        logger.info(f"批量查询结果: {batch_result}")
        
        # 示例4: 查询信用报告
        logger.info("=== 信用报告查询示例 ===")
        if personal_result.get("data", {}).get("reportId"):
            report_result = info_api.query_credit_report(
                report_id=personal_result["data"]["reportId"],
                report_type="personal"
            )
            logger.info(f"信用报告查询结果: {report_result}")
        
        # 示例5: 获取查询历史记录
        logger.info("=== 查询历史记录示例 ===")
        history_result = info_api.get_query_history(
            start_date="2023-01-01",
            end_date="2023-12-31",
            page=1,
            page_size=10
        )
        logger.info(f"查询历史记录: {history_result}")
        
    except NifaError as e:
        logger.error(f"NIFA API错误: {e}")
        logger.error(f"错误代码: {e.error_code}")
        logger.error(f"错误详情: {e.details}")
        
    except Exception as e:
        logger.error(f"未知错误: {e}")
        
    finally:
        # 关闭API客户端
        info_api.close()
        logger.info("API客户端已关闭")


def context_manager_example():
    """使用上下文管理器的示例"""
    logger.info("=== 上下文管理器使用示例 ===")
    
    try:
        with InfoAPI() as info_api:
            result = info_api.query_personal_credit(
                id_card="110101199001011234",
                name="张三"
            )
            logger.info(f"查询结果: {result}")
            
    except NifaError as e:
        logger.error(f"查询失败: {e}")


def error_handling_example():
    """错误处理示例"""
    logger.info("=== 错误处理示例 ===")
    
    info_api = InfoAPI()
    
    try:
        # 故意使用无效的身份证号来演示错误处理
        info_api.query_personal_credit(
            id_card="invalid_id_card",
            name="张三"
        )
        
    except NifaError as e:
        logger.error(f"验证错误: {e}")
        logger.error(f"错误字段: {e.details.get('field')}")
        logger.error(f"错误值: {e.details.get('value')}")
        
    finally:
        info_api.close()


def custom_client_example():
    """自定义客户端配置示例"""
    logger.info("=== 自定义客户端配置示例 ===")
    
    from nifa.api.client import APIClient
    
    # 创建自定义配置的客户端
    custom_client = APIClient(
        base_url="https://test-api.nifa.org.cn",
        org_code="TEST123456",
        timeout=60,
        max_retries=5
    )
    
    try:
        with InfoAPI(client=custom_client) as info_api:
            result = info_api.query_personal_credit(
                id_card="110101199001011234",
                name="张三"
            )
            logger.info(f"自定义客户端查询结果: {result}")
            
    except NifaError as e:
        logger.error(f"查询失败: {e}")
    
    finally:
        custom_client.close()


if __name__ == "__main__":
    # 运行主示例
    main()
    
    # 运行其他示例
    context_manager_example()
    error_handling_example()
    custom_client_example()
