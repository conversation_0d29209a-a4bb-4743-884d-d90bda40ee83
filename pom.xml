<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.org.nifa</groupId>
	<artifactId>bxcredit</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>bxcredit</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.7</version>
			<scope>test</scope>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.sun.jersey/jersey-client -->
		<dependency>
			<groupId>com.sun.jersey</groupId>
			<artifactId>jersey-client</artifactId>
			<version>1.19</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.sun.jersey/jersey-json -->
		<dependency>
			<groupId>com.sun.jersey</groupId>
			<artifactId>jersey-json</artifactId>
			<version>1.19</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/net.sf.json-lib/json-lib -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<classifier>jdk13</classifier>
			<version>2.4</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.codehaus.jettison/jettison -->
		<!-- <dependency> -->
		<!-- <groupId>org.codehaus.jettison</groupId> -->
		<!-- <artifactId>jettison</artifactId> -->
		<!-- <version>1.1</version> -->
		<!-- </dependency> -->


		<!-- https://mvnrepository.com/artifact/org.apache.struts.xwork/xwork-core -->
		<dependency>
			<groupId>org.apache.struts.xwork</groupId>
			<artifactId>xwork-core</artifactId>
			<version>2.3.16.3</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.52</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk15on -->
		<!-- <dependency> -->
		<!-- <groupId>org.bouncycastle</groupId> -->
		<!-- <artifactId>bcprov-jdk16</artifactId> -->
		<!-- <version>1.40</version> -->
		<!-- </dependency> -->
		<!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
		</dependency>


		<dependency>
			<groupId>com.cfcc.jaf</groupId>
			<artifactId>crypto</artifactId>
			<version>1.5</version>
			<scope>system</scope>
			<systemPath>${basedir}/lib/com.cfcc.jaf.crypto.jar</systemPath>
		</dependency>
		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>

	</dependencies>
</project>
