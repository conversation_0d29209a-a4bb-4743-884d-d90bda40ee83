"""
信用信息查询API测试
"""

import pytest
import responses
from unittest.mock import Mock, patch

from nifa.api.info import InfoAPI
from nifa.exceptions.base import Ni<PERSON>ValidationError, NifaResponseError


class TestInfoAPI:
    """信用信息查询API测试"""
    
    def test_init_with_client(self, mock_api_client):
        """测试使用提供的客户端初始化"""
        api = InfoAPI(client=mock_api_client)
        assert api.client == mock_api_client
        assert api._own_client is False
    
    def test_init_without_client(self):
        """测试不提供客户端时自动创建"""
        api = InfoAPI()
        assert api.client is not None
        assert api._own_client is True
    
    def test_query_personal_credit_success(self, mock_api_client, sample_personal_data, mock_response_success):
        """测试个人信用查询成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = InfoAPI(client=mock_api_client)
        result = api.query_personal_credit(
            id_card=sample_personal_data["idCard"],
            name=sample_personal_data["name"]
        )
        
        assert result == mock_response_success
        mock_api_client.post.assert_called_once()
        
        # 验证调用参数
        call_args = mock_api_client.post.call_args
        assert call_args[0][0] == "personal/credit/query"
        assert call_args[1]["data"]["idCard"] == sample_personal_data["idCard"]
        assert call_args[1]["data"]["name"] == sample_personal_data["name"]
        assert call_args[1]["data"]["queryType"] == "01"  # 默认值
    
    def test_query_personal_credit_with_custom_params(self, mock_api_client, sample_personal_data):
        """测试带自定义参数的个人信用查询"""
        api = InfoAPI(client=mock_api_client)
        api.query_personal_credit(
            id_card=sample_personal_data["idCard"],
            name=sample_personal_data["name"],
            query_type="02",
            query_reason="详细查询"
        )
        
        call_args = mock_api_client.post.call_args
        assert call_args[1]["data"]["queryType"] == "02"
        assert call_args[1]["data"]["queryReason"] == "详细查询"
    
    def test_query_personal_credit_invalid_id_card(self, mock_api_client):
        """测试无效身份证号"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_personal_credit(
                id_card="invalid_id",
                name="张三"
            )
        assert "身份证号码" in str(exc_info.value)
    
    def test_query_personal_credit_invalid_name(self, mock_api_client, sample_personal_data):
        """测试无效姓名"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_personal_credit(
                id_card=sample_personal_data["idCard"],
                name=""
            )
        assert "姓名不能为空" in str(exc_info.value)
    
    def test_query_personal_credit_invalid_query_type(self, mock_api_client, sample_personal_data):
        """测试无效查询类型"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_personal_credit(
                id_card=sample_personal_data["idCard"],
                name=sample_personal_data["name"],
                query_type="03"  # 无效类型
            )
        assert "查询类型必须为01" in str(exc_info.value)
    
    def test_query_enterprise_credit_success(self, mock_api_client, sample_enterprise_data, mock_response_success):
        """测试企业信用查询成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = InfoAPI(client=mock_api_client)
        result = api.query_enterprise_credit(
            org_code=sample_enterprise_data["orgCode"],
            org_name=sample_enterprise_data["orgName"]
        )
        
        assert result == mock_response_success
        mock_api_client.post.assert_called_once()
        
        # 验证调用参数
        call_args = mock_api_client.post.call_args
        assert call_args[0][0] == "enterprise/credit/query"
        assert call_args[1]["data"]["orgCode"] == sample_enterprise_data["orgCode"]
        assert call_args[1]["data"]["orgName"] == sample_enterprise_data["orgName"]
    
    def test_query_enterprise_credit_invalid_org_code(self, mock_api_client):
        """测试无效企业代码"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_enterprise_credit(
                org_code="",
                org_name="测试企业"
            )
        assert "企业代码不能为空" in str(exc_info.value)
    
    def test_query_credit_report_success(self, mock_api_client, mock_response_success):
        """测试信用报告查询成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = InfoAPI(client=mock_api_client)
        result = api.query_credit_report(
            report_id="REPORT123456",
            report_type="personal"
        )
        
        assert result == mock_response_success
        mock_api_client.post.assert_called_once()
        
        call_args = mock_api_client.post.call_args
        assert call_args[0][0] == "credit/report/query"
        assert call_args[1]["data"]["reportId"] == "REPORT123456"
        assert call_args[1]["data"]["reportType"] == "personal"
    
    def test_query_credit_report_invalid_report_id(self, mock_api_client):
        """测试无效报告ID"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_credit_report(
                report_id="",
                report_type="personal"
            )
        assert "报告ID不能为空" in str(exc_info.value)
    
    def test_query_credit_report_invalid_report_type(self, mock_api_client):
        """测试无效报告类型"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.query_credit_report(
                report_id="REPORT123456",
                report_type="invalid"
            )
        assert "报告类型必须为personal" in str(exc_info.value)
    
    def test_batch_query_personal_credit_success(self, mock_api_client, mock_response_success):
        """测试批量个人信用查询成功"""
        mock_api_client.post.return_value = mock_response_success
        
        query_list = [
            {"idCard": "110101199001011234", "name": "张三"},
            {"idCard": "110101199001011235", "name": "李四"}
        ]
        
        api = InfoAPI(client=mock_api_client)
        result = api.batch_query_personal_credit(query_list=query_list)
        
        assert result == mock_response_success
        mock_api_client.post.assert_called_once()
        
        call_args = mock_api_client.post.call_args
        assert call_args[0][0] == "personal/credit/batch-query"
        assert call_args[1]["data"]["queryList"] == query_list
    
    def test_batch_query_personal_credit_empty_list(self, mock_api_client):
        """测试空查询列表"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_query_personal_credit(query_list=[])
        assert "查询列表不能为空" in str(exc_info.value)
    
    def test_batch_query_personal_credit_too_many_items(self, mock_api_client):
        """测试查询列表过多"""
        api = InfoAPI(client=mock_api_client)
        
        # 创建超过限制的查询列表
        query_list = [
            {"idCard": f"11010119900101{i:04d}", "name": f"用户{i}"}
            for i in range(101)
        ]
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_query_personal_credit(query_list=query_list)
        assert "批量查询数量不能超过100条" in str(exc_info.value)
    
    def test_batch_query_personal_credit_invalid_item(self, mock_api_client):
        """测试查询列表包含无效项"""
        api = InfoAPI(client=mock_api_client)
        
        query_list = [
            {"idCard": "110101199001011234", "name": "张三"},
            "invalid_item"  # 无效项
        ]
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.batch_query_personal_credit(query_list=query_list)
        assert "查询列表第2项必须为字典" in str(exc_info.value)
    
    def test_get_query_history_success(self, mock_api_client, mock_response_success):
        """测试获取查询历史成功"""
        mock_api_client.post.return_value = mock_response_success
        
        api = InfoAPI(client=mock_api_client)
        result = api.get_query_history(
            start_date="2023-01-01",
            end_date="2023-12-31"
        )
        
        assert result == mock_response_success
        mock_api_client.post.assert_called_once()
        
        call_args = mock_api_client.post.call_args
        assert call_args[0][0] == "query/history"
        assert call_args[1]["data"]["startDate"] == "2023-01-01"
        assert call_args[1]["data"]["endDate"] == "2023-12-31"
        assert call_args[1]["data"]["page"] == 1
        assert call_args[1]["data"]["pageSize"] == 20
    
    def test_get_query_history_invalid_date(self, mock_api_client):
        """测试无效日期格式"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_query_history(
                start_date="2023/01/01",  # 错误格式
                end_date="2023-12-31"
            )
        assert "日期格式不正确" in str(exc_info.value)
    
    def test_get_query_history_invalid_page(self, mock_api_client):
        """测试无效页码"""
        api = InfoAPI(client=mock_api_client)
        
        with pytest.raises(NifaValidationError) as exc_info:
            api.get_query_history(
                start_date="2023-01-01",
                end_date="2023-12-31",
                page=0  # 无效页码
            )
        assert "页码必须大于0" in str(exc_info.value)
    
    def test_context_manager(self, mock_api_client):
        """测试上下文管理器"""
        with InfoAPI(client=mock_api_client) as api:
            assert api.client == mock_api_client
        # 上下文退出后应该调用close方法
    
    def test_close_own_client(self):
        """测试关闭自有客户端"""
        api = InfoAPI()
        api.client.close = Mock()
        
        api.close()
        api.client.close.assert_called_once()
    
    def test_close_external_client(self, mock_api_client):
        """测试不关闭外部客户端"""
        mock_api_client.close = Mock()
        api = InfoAPI(client=mock_api_client)
        
        api.close()
        mock_api_client.close.assert_not_called()
