package cn.org.nifa.bxcredit;

import java.util.HashMap;
import java.util.Map;

import com.sun.jersey.api.client.ClientResponse;

// [查询接口]
public class HighcourtDemo {
	private static Map<String, String> getHeaderMap(Map<String, String> map) {
		// 
		String scode = Utils.getRandomNumber(10); //10位随机数
		// 构造签名 用于身份验证
		String preparedSign = Config.ORGCODE 
				+ scode 
				+ map.get("sname")
				+ map.get("stype")
				+ map.get("sreason")
				+ map.get("sno")
				+ Config.KEY;
		String sign = Utils.sha256(preparedSign);
		// 构造header
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sbankcode", Config.ORGCODE);  
		headerMap.put("scode", scode);  
		headerMap.put("sign", sign);  
		return headerMap;
		
	}
	public static void main(String[] args) {
		// 构造client--耗费资源 适合多次使用
		JerseyClient client = new JerseyClient(Config.HIGHCOURT_URI);
		ClientResponse resp = null;
		
		// 构造body
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("sname", "许文");
		bodyMap.put("stype", "0");  
		bodyMap.put("sreason", "b");  
		bodyMap.put("sno", "640221198509101292");
		System.out.println("[body]: \n" + Utils.map2Json(bodyMap));
		// 构造header
		Map<String, String> headerMap = getHeaderMap(bodyMap);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp.getStatus() == 200) {
			System.out.println(resp.getEntity(String.class));
		}else {
			System.out.println("[ERROR] Status: " + resp.getStatus());
			System.out.println(resp.getEntity(String.class));
		}
	}
}
