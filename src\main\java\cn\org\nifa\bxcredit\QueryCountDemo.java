package cn.org.nifa.bxcredit;

import java.util.HashMap;
import java.util.Map;

import com.sun.jersey.api.client.ClientResponse;

import net.sf.json.JSONObject;

//[查询量耗用查看接口]
//用于查询最大查询量和当月查询量耗用情况
//生产上是每天更新查询量，每天查询一次即可
public class QueryCountDemo {
	private static Map<String, String> getHeaderMap(Map<String, String> map) {
		// 构造header
		// header("sbankcode", sbankcode)
		String scode = Utils.getRandomNumber(10); //10位随机数
		Map<String, String> headerMap = new HashMap<String, String>();
		String preparedSign = Config.ORGCODE 
				+ scode 
				+ map.get("sdate")
				+ map.get("stype")
				+ Config.KEY;
		String sign = Utils.sha256(preparedSign); 
		headerMap.put("sbankcode", Config.ORGCODE);  
		headerMap.put("scode", scode);  
		headerMap.put("sign", sign);  
		
		return headerMap;
		
	}
	public static void main(String[] args) {
		// 构造client--耗费资源 适合多次使用
		System.out.println(Config.QUERY_COUNT_URI);
		JerseyClient client = new JerseyClient(Config.QUERY_COUNT_URI);
		ClientResponse resp = null;
		
		Map bodyMap = new HashMap<String, String>();
		bodyMap.put("stype", "2");
		bodyMap.put("sdate", "202209");
		System.out.println("[body]: \n" + Utils.map2Json(bodyMap));

		// 构造header
		Map<String, String> headerMap = getHeaderMap(bodyMap);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp.getStatus() == 200) {
			/*
			 *  不修改任何配置时默认返回：
			* {
			*	    "maxquerycount": "0",
			*	    "querystate": "0",
			*	    "sumquerycount": "0",
			*	    "sdate": "201903",
			*	    "msgCode": "200",
			*	    "msgContent": "成功!"
			*	}
			 */
			String responseString = resp.getEntity(String.class);
			System.out.println(responseString.toString());
			JSONObject json = JSONObject.fromObject(responseString.toString());
			System.out.println(json);

		}else {
			System.out.println("[ERROR] Status: " + resp.getStatus());
			System.out.println(resp.getEntity(String.class));
		}
	}
}
