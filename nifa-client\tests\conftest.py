"""
pytest配置文件
定义测试夹具和配置
"""

import pytest
import os
from unittest.mock import Mock

from nifa.config.settings import Settings
from nifa.api.client import APIClient


@pytest.fixture
def test_settings():
    """测试配置夹具"""
    return Settings(
        ENVIRONMENT="testing",
        DEBUG=True,
        NIFA_BASE_URL="https://test-api.nifa.org.cn",
        NIFA_ORG_CODE="TEST123456",
        NIFA_TIMEOUT=10,
        NIFA_MAX_RETRIES=1,
        NIFA_RETRY_DELAY=0.1,
        LOG_LEVEL="DEBUG"
    )


@pytest.fixture
def mock_api_client():
    """模拟API客户端夹具"""
    client = Mock(spec=APIClient)
    client.post.return_value = {
        "code": "0000",
        "message": "成功",
        "data": {}
    }
    client.get.return_value = {
        "code": "0000", 
        "message": "成功",
        "data": {}
    }
    return client


@pytest.fixture
def sample_personal_data():
    """个人信息测试数据"""
    return {
        "idCard": "110101199001011234",
        "name": "张三"
    }


@pytest.fixture
def sample_enterprise_data():
    """企业信息测试数据"""
    return {
        "orgCode": "91110000123456789X",
        "orgName": "测试企业有限公司"
    }


@pytest.fixture
def mock_response_success():
    """成功响应数据"""
    return {
        "code": "0000",
        "message": "查询成功",
        "data": {
            "queryId": "TEST123456789",
            "result": "查询结果"
        }
    }


@pytest.fixture
def mock_response_error():
    """错误响应数据"""
    return {
        "code": "1001",
        "message": "参数错误",
        "data": None
    }


@pytest.fixture(autouse=True)
def setup_test_env(monkeypatch):
    """自动设置测试环境变量"""
    monkeypatch.setenv("NIFA_ORG_CODE", "TEST123456")
    monkeypatch.setenv("NIFA_BASE_URL", "https://test-api.nifa.org.cn")
    monkeypatch.setenv("ENVIRONMENT", "testing")


@pytest.fixture
def temp_file(tmp_path):
    """临时文件夹具"""
    file_path = tmp_path / "test_file.txt"
    file_path.write_text("测试文件内容", encoding="utf-8")
    return str(file_path)
