package cn.org.nifa.bxcredit;

import java.util.Arrays;

import com.cfcc.jaf.crypto.CryptoUtil;
import com.cfcc.jaf.crypto.sm.SMUtil;



public class TestSM2 {


	    public static void main(String[] args) {
			byte b[]= SMUtil.genSM2KeyPair();
			byte[] keypr=Arrays.copyOfRange(b,0,32);
			System.out.println(keypr.length+"私钥："+CryptoUtil.toHexString(keypr));
			byte[] keypu1=Arrays.copyOfRange(b,32,64);
			System.out.println(keypu1.length+"公钥1："+CryptoUtil.toHexString(keypu1));
			byte[] keypu2=Arrays.copyOfRange(b,64,96);
			System.out.println(keypu2.length+"公钥2："+CryptoUtil.toHexString(keypu2));
	    }

	

}
