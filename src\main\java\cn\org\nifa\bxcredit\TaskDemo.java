package cn.org.nifa.bxcredit;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;

import com.sun.jersey.api.client.ClientResponse;

import net.sf.json.JSONObject;

//[报送状态查看接口]
//用于查看已报送数据的处理状态 处理状态如：等待处理、正在处理、处理完毕有反馈、处理完毕无反馈
//数据处理时间5~30分钟不等，一般数据报送1小时后调用本接口即可

public class TaskDemo {
	private static Map<String, String> getHeaderMap(Map<String, String> map) {
		// 
		String scode = Utils.getRandomNumber(10); //10位随机数
		// 构造签名 用于身份验证
		// sbankcode + scode + sdatacode + "cfcc";
		String preparedSign = Config.ORGCODE // sbankcode
				+ scode //scode
				+ map.get("sdatacode")  //sdatacode
				+ Config.KEY;  //key
		String sign = Utils.sha256(preparedSign);
		// 构造header
		// header("sbankcode", sbankcode).header("scode", scode).header("sign", sign).header("sdatacode", sdatacode)
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sbankcode", Config.ORGCODE);  
		headerMap.put("scode", scode);  
		headerMap.put("sign", sign);  
		headerMap.put("sdatacode", map.get("sdatacode"));  
		return headerMap;
		
	}
	public static void main(String[] args) {
		// 构造client--耗费资源 适合多次使用
		JerseyClient client = new JerseyClient(Config.TASK_URI);
		ClientResponse resp = null;
		
		// 构造body
		Map<String, String> preparedMap = new HashMap<String, String>();
		String filename = "C1B476EA0202309051715127112";
		preparedMap.put("sdatacode", filename);  
		System.out.println("[preparedMap]: \n" + Utils.map2Json(preparedMap));
		// 构造header
		Map<String, String> headerMap = getHeaderMap(preparedMap);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		Map bodyMap = new HashMap<String, String>();
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp!= null && resp.getStatus() == 200) {
			String responseString = resp.getEntity(String.class);
			System.out.println(responseString);
			JSONObject json = JSONObject.fromObject(responseString.toString());
			// 如果有反馈文件 说明有数据未成功入库 
			// 反馈报文保存为txt文件 
			if (json.containsKey("returnresultdata") && !json.get("returnresultdata").equals(null)) {
				File decEncFile = new File("tmp/test.enc"); //解密enc文件
				try {
					FileUtils.writeByteArrayToFile(decEncFile,Base64.decodeBase64(json.get("returnresultdata").toString()));
				} catch (IOException e) {
					e.printStackTrace();
				}
				Utils.deccryptFile(decEncFile.getPath(), "tmp/test.txt"); //解密结果。里面的错误代码从《采集标准》查询
				decEncFile.delete();
				System.out.println("存在入库失败的数据，见tmp/test.txt" );
			}
		}else {
			System.out.println("[ERROR] resp: " + resp);
		}
	}
}
