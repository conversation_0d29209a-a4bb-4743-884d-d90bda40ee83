package cn.org.nifa.bxcredit;

import java.util.HashMap;
import java.util.Map;

import com.sun.jersey.api.client.ClientResponse;

// [查询接口]
public class InfoMd5Demo {
	private static Map<String, String> getHeaderMap(Map<String, String> map) {
		// 
//		String scode = Utils.getRandomNumber(10); //10位随机数
		String scode = "**********"; //10位随机数
		System.out.println(scode);
		// 构造签名 用于身份验证
		String preparedSign = Config.ORGCODE 
				+ scode 
				+ map.get("md5")
				+ map.get("sreason")
				+ Config.KEY;
		System.out.println(preparedSign);
		String sign = Utils.sha256(preparedSign);
		// 构造header
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sbankcode", Config.ORGCODE);  
		headerMap.put("scode", scode);
		headerMap.put("sign", sign);
		return headerMap;
		
	}
	public static void main(String[] args) {
		
		for(int i=0;i<1;i++){
		// 构造client--耗费资源 适合多次使用
		JerseyClient client = new JerseyClient(Config.INFO_URI_MD5);
		ClientResponse resp = null;
		
		// 构造body
		Map<String, String> bodyMap = new HashMap<String, String>();
		//企业共享查询接口-密文//bodyMap.put("md5", Utils.string2Md5(Utils.string2Md5("赵1")+"password")+"0"+Utils.string2Md5(Utils.string2Md5("1234567880000000001")+"password"));
		//实时查询不要添加，只为追溯用户历史报告 //bodyMap.put("ddate", "202203");
		//bodyMap.put("md5", Utils.string2Md5(Utils.string2Md5("用户姓名")+"固定值password")+"用户证件类型，默认为0身份证"+Utils.string2Md5(Utils.string2Md5("用户证件号码")+"固定值password"));
		bodyMap.put("sreason", "b");
		bodyMap.put("md5", Utils.string2Md5(Utils.string2Md5("马刚")+"password")+"0"+Utils.string2Md5(Utils.string2Md5("440901196611136279")+"password"));
//		bodyMap.put("md5", "3000006067fa9fad20d2f98fb2e506ba0c5418085a04e22c73bae799e685d1e91");
		System.out.println("[body]: \n" + Utils.map2Json(bodyMap));

		// 构造header
		Map<String, String> headerMap = getHeaderMap(bodyMap);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp.getStatus() == 200) {
			System.out.println(resp.getEntity(String.class));
			/*
			 * 不修改任何配置时默认返回：
			 * {"loancount":"58","loanamt":"5800000","outstandcount":"58","loanbal":"5800000",
			 * "generationcount":"0","generationamount":"0","overduecount":"0","overdueamt":"0",
			 * "overduemorecount":"0","overduemoreamt":"0",
			 * "totalorg":"最近1个月被6家机构查询",
			 * "queryatotalorg":"最近1个月被6家机构以“贷前审批”原因查询",
			 * "infoquerybean":[{"ordernum":"1","ddate":"2018-10-23","s_value":"贷前审批"},{"ordernum":"2","ddate":"2018-10-23","s_value":"其他"......}],
			 * "msgCode":"200","msgContent":"成功!"}
			 */
		}else {
			System.out.println("[ERROR] Status: " + resp.getStatus());
			System.out.println(resp.getEntity(String.class));
		}
		}
	}
}
//  86d61daa174c3139581170881d09c36590d797c1c661490a746e7d4bface1395