# These stubs are forked from typeshed, since we use some definitions that only make
# sense in the context of mypy/mypyc (in particular, native int types such as i64).

import abc
import sys
from _collections_abc import dict_items, dict_keys, dict_values
from _typeshed import IdentityFunction, Self
from collections.abc import Mapping
from typing import Any, ClassVar, Generic, SupportsInt, TypeVar, overload, type_check_only
from typing_extensions import Never, SupportsIndex
from _typeshed import ReadableBuffer, SupportsTrunc

_T = TypeVar("_T")
_U = TypeVar("_U")

# Internal mypy fallback type for all typed dicts (does not exist at runtime)
# N.B. Keep this mostly in sync with typing(_extensions)._TypedDict
@type_check_only
class _TypedDict(Mapping[str, object], metaclass=abc.ABCMeta):
    __total__: ClassVar[bool]
    # Unlike typing(_extensions).TypedDict,
    # subclasses of mypy_extensions.TypedDict do NOT have the __required_keys__ and __optional_keys__ ClassVars
    def copy(self: Self) -> Self: ...
    # Using Never so that only calls using mypy plugin hook that specialize the signature
    # can go through.
    def setdefault(self, k: Never, default: object) -> object: ...
    # Mypy plugin hook for 'pop' expects that 'default' has a type variable type.
    def pop(self, k: Never, default: _T = ...) -> object: ...  # pyright: ignore[reportInvalidTypeVarUse]
    def update(self: Self, __m: Self) -> None: ...
    def items(self) -> dict_items[str, object]: ...
    def keys(self) -> dict_keys[str, object]: ...
    def values(self) -> dict_values[str, object]: ...
    def __delitem__(self, k: Never) -> None: ...
    if sys.version_info >= (3, 9):
        def __or__(self: Self, __other: Self) -> Self: ...
        def __ior__(self: Self, __other: Self) -> Self: ...

def TypedDict(typename: str, fields: dict[str, type[Any]], total: bool = ...) -> type[dict[str, Any]]: ...
@overload
def Arg(type: _T, name: str | None = ...) -> _T: ...
@overload
def Arg(*, name: str | None = ...) -> Any: ...
@overload
def DefaultArg(type: _T, name: str | None = ...) -> _T: ...
@overload
def DefaultArg(*, name: str | None = ...) -> Any: ...
@overload
def NamedArg(type: _T, name: str | None = ...) -> _T: ...
@overload
def NamedArg(*, name: str | None = ...) -> Any: ...
@overload
def DefaultNamedArg(type: _T, name: str | None = ...) -> _T: ...
@overload
def DefaultNamedArg(*, name: str | None = ...) -> Any: ...
@overload
def VarArg(type: _T) -> _T: ...
@overload
def VarArg() -> Any: ...
@overload
def KwArg(type: _T) -> _T: ...
@overload
def KwArg() -> Any: ...

# Return type that indicates a function does not return.
# Deprecated: Use typing.NoReturn instead.
class NoReturn: ...

# This is consistent with implementation. Usage intends for this as
# a class decorator, but mypy does not support type[_T] for abstract
# classes until this issue is resolved, https://github.com/python/mypy/issues/4717.
def trait(cls: _T) -> _T: ...
def mypyc_attr(*attrs: str, **kwattrs: object) -> IdentityFunction: ...

class FlexibleAlias(Generic[_T, _U]): ...

# Native int types such as i64 are magical and support implicit
# coercions to/from int using special logic in mypy. We generally only
# include operations here for which we have specialized primitives.

class i64:
    @overload
    def __new__(cls, __x: str | ReadableBuffer | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> i64: ...
    @overload
    def __new__(cls, __x: str | bytes | bytearray, base: SupportsIndex) -> i64: ...

    def __add__(self, x: i64) -> i64: ...
    def __radd__(self, x: i64) -> i64: ...
    def __sub__(self, x: i64) -> i64: ...
    def __rsub__(self, x: i64) -> i64: ...
    def __mul__(self, x: i64) -> i64: ...
    def __rmul__(self, x: i64) -> i64: ...
    def __floordiv__(self, x: i64) -> i64: ...
    def __rfloordiv__(self, x: i64) -> i64: ...
    def __mod__(self, x: i64) -> i64: ...
    def __rmod__(self, x: i64) -> i64: ...
    def __and__(self, x: i64) -> i64: ...
    def __rand__(self, x: i64) -> i64: ...
    def __or__(self, x: i64) -> i64: ...
    def __ror__(self, x: i64) -> i64: ...
    def __xor__(self, x: i64) -> i64: ...
    def __rxor__(self, x: i64) -> i64: ...
    def __lshift__(self, x: i64) -> i64: ...
    def __rlshift__(self, x: i64) -> i64: ...
    def __rshift__(self, x: i64) -> i64: ...
    def __rrshift__(self, x: i64) -> i64: ...
    def __neg__(self) -> i64: ...
    def __invert__(self) -> i64: ...
    def __pos__(self) -> i64: ...
    def __lt__(self, x: i64) -> bool: ...
    def __le__(self, x: i64) -> bool: ...
    def __ge__(self, x: i64) -> bool: ...
    def __gt__(self, x: i64) -> bool: ...
    def __index__(self) -> int: ...

class i32:
    @overload
    def __new__(cls, __x: str | ReadableBuffer | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> i32: ...
    @overload
    def __new__(cls, __x: str | bytes | bytearray, base: SupportsIndex) -> i32: ...

    def __add__(self, x: i32) -> i32: ...
    def __radd__(self, x: i32) -> i32: ...
    def __sub__(self, x: i32) -> i32: ...
    def __rsub__(self, x: i32) -> i32: ...
    def __mul__(self, x: i32) -> i32: ...
    def __rmul__(self, x: i32) -> i32: ...
    def __floordiv__(self, x: i32) -> i32: ...
    def __rfloordiv__(self, x: i32) -> i32: ...
    def __mod__(self, x: i32) -> i32: ...
    def __rmod__(self, x: i32) -> i32: ...
    def __and__(self, x: i32) -> i32: ...
    def __rand__(self, x: i32) -> i32: ...
    def __or__(self, x: i32) -> i32: ...
    def __ror__(self, x: i32) -> i32: ...
    def __xor__(self, x: i32) -> i32: ...
    def __rxor__(self, x: i32) -> i32: ...
    def __lshift__(self, x: i32) -> i32: ...
    def __rlshift__(self, x: i32) -> i32: ...
    def __rshift__(self, x: i32) -> i32: ...
    def __rrshift__(self, x: i32) -> i32: ...
    def __neg__(self) -> i32: ...
    def __invert__(self) -> i32: ...
    def __pos__(self) -> i32: ...
    def __lt__(self, x: i32) -> bool: ...
    def __le__(self, x: i32) -> bool: ...
    def __ge__(self, x: i32) -> bool: ...
    def __gt__(self, x: i32) -> bool: ...
    def __index__(self) -> int: ...

class i16:
    @overload
    def __new__(cls, __x: str | ReadableBuffer | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> i16: ...
    @overload
    def __new__(cls, __x: str | bytes | bytearray, base: SupportsIndex) -> i16: ...

    def __add__(self, x: i16) -> i16: ...
    def __radd__(self, x: i16) -> i16: ...
    def __sub__(self, x: i16) -> i16: ...
    def __rsub__(self, x: i16) -> i16: ...
    def __mul__(self, x: i16) -> i16: ...
    def __rmul__(self, x: i16) -> i16: ...
    def __floordiv__(self, x: i16) -> i16: ...
    def __rfloordiv__(self, x: i16) -> i16: ...
    def __mod__(self, x: i16) -> i16: ...
    def __rmod__(self, x: i16) -> i16: ...
    def __and__(self, x: i16) -> i16: ...
    def __rand__(self, x: i16) -> i16: ...
    def __or__(self, x: i16) -> i16: ...
    def __ror__(self, x: i16) -> i16: ...
    def __xor__(self, x: i16) -> i16: ...
    def __rxor__(self, x: i16) -> i16: ...
    def __lshift__(self, x: i16) -> i16: ...
    def __rlshift__(self, x: i16) -> i16: ...
    def __rshift__(self, x: i16) -> i16: ...
    def __rrshift__(self, x: i16) -> i16: ...
    def __neg__(self) -> i16: ...
    def __invert__(self) -> i16: ...
    def __pos__(self) -> i16: ...
    def __lt__(self, x: i16) -> bool: ...
    def __le__(self, x: i16) -> bool: ...
    def __ge__(self, x: i16) -> bool: ...
    def __gt__(self, x: i16) -> bool: ...
    def __index__(self) -> int: ...

class u8:
    @overload
    def __new__(cls, __x: str | ReadableBuffer | SupportsInt | SupportsIndex | SupportsTrunc = ...) -> u8: ...
    @overload
    def __new__(cls, __x: str | bytes | bytearray, base: SupportsIndex) -> u8: ...

    def __add__(self, x: u8) -> u8: ...
    def __radd__(self, x: u8) -> u8: ...
    def __sub__(self, x: u8) -> u8: ...
    def __rsub__(self, x: u8) -> u8: ...
    def __mul__(self, x: u8) -> u8: ...
    def __rmul__(self, x: u8) -> u8: ...
    def __floordiv__(self, x: u8) -> u8: ...
    def __rfloordiv__(self, x: u8) -> u8: ...
    def __mod__(self, x: u8) -> u8: ...
    def __rmod__(self, x: u8) -> u8: ...
    def __and__(self, x: u8) -> u8: ...
    def __rand__(self, x: u8) -> u8: ...
    def __or__(self, x: u8) -> u8: ...
    def __ror__(self, x: u8) -> u8: ...
    def __xor__(self, x: u8) -> u8: ...
    def __rxor__(self, x: u8) -> u8: ...
    def __lshift__(self, x: u8) -> u8: ...
    def __rlshift__(self, x: u8) -> u8: ...
    def __rshift__(self, x: u8) -> u8: ...
    def __rrshift__(self, x: u8) -> u8: ...
    def __neg__(self) -> u8: ...
    def __invert__(self) -> u8: ...
    def __pos__(self) -> u8: ...
    def __lt__(self, x: u8) -> bool: ...
    def __le__(self, x: u8) -> bool: ...
    def __ge__(self, x: u8) -> bool: ...
    def __gt__(self, x: u8) -> bool: ...
    def __index__(self) -> int: ...
