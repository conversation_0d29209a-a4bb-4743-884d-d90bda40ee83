import sys
from typing import Literal, NoReturn, Protocol, type_check_only
from xml.sax import xmlreader

version: str

@type_check_only
class _ErrorHandlerProtocol(Protocol):  # noqa: Y046  # Protocol is not used
    def error(self, exception: BaseException) -> NoReturn: ...
    def fatalError(self, exception: BaseException) -> NoReturn: ...
    def warning(self, exception: BaseException) -> None: ...

class ErrorHandler:
    def error(self, exception: BaseException) -> NoReturn: ...
    def fatalError(self, exception: BaseException) -> NoReturn: ...
    def warning(self, exception: BaseException) -> None: ...

@type_check_only
class _ContentHandlerProtocol(Protocol):  # noqa: Y046  # Protocol is not used
    def setDocumentLocator(self, locator: xmlreader.Locator) -> None: ...
    def startDocument(self) -> None: ...
    def endDocument(self) -> None: ...
    def startPrefixMapping(self, prefix: str | None, uri: str) -> None: ...
    def endPrefixMapping(self, prefix: str | None) -> None: ...
    def startElement(self, name: str, attrs: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def startElementNS(self, name: tuple[str | None, str], qname: str | None, attrs: xmlreader.AttributesNSImpl) -> None: ...
    def endElementNS(self, name: tuple[str | None, str], qname: str | None) -> None: ...
    def characters(self, content: str) -> None: ...
    def ignorableWhitespace(self, whitespace: str) -> None: ...
    def processingInstruction(self, target: str, data: str) -> None: ...
    def skippedEntity(self, name: str) -> None: ...

class ContentHandler:
    def setDocumentLocator(self, locator: xmlreader.Locator) -> None: ...
    def startDocument(self) -> None: ...
    def endDocument(self) -> None: ...
    def startPrefixMapping(self, prefix: str | None, uri: str) -> None: ...
    def endPrefixMapping(self, prefix: str | None) -> None: ...
    def startElement(self, name: str, attrs: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def startElementNS(self, name: tuple[str | None, str], qname: str | None, attrs: xmlreader.AttributesNSImpl) -> None: ...
    def endElementNS(self, name: tuple[str | None, str], qname: str | None) -> None: ...
    def characters(self, content: str) -> None: ...
    def ignorableWhitespace(self, whitespace: str) -> None: ...
    def processingInstruction(self, target: str, data: str) -> None: ...
    def skippedEntity(self, name: str) -> None: ...

@type_check_only
class _DTDHandlerProtocol(Protocol):  # noqa: Y046  # Protocol is not used
    def notationDecl(self, name: str, publicId: str | None, systemId: str) -> None: ...
    def unparsedEntityDecl(self, name: str, publicId: str | None, systemId: str, ndata: str) -> None: ...

class DTDHandler:
    def notationDecl(self, name: str, publicId: str | None, systemId: str) -> None: ...
    def unparsedEntityDecl(self, name: str, publicId: str | None, systemId: str, ndata: str) -> None: ...

@type_check_only
class _EntityResolverProtocol(Protocol):  # noqa: Y046  # Protocol is not used
    def resolveEntity(self, publicId: str | None, systemId: str) -> str: ...

class EntityResolver:
    def resolveEntity(self, publicId: str | None, systemId: str) -> str: ...

feature_namespaces: str
feature_namespace_prefixes: str
feature_string_interning: str
feature_validation: str
feature_external_ges: str
feature_external_pes: str
all_features: list[str]
property_lexical_handler: Literal["http://xml.org/sax/properties/lexical-handler"]
property_declaration_handler: Literal["http://xml.org/sax/properties/declaration-handler"]
property_dom_node: Literal["http://xml.org/sax/properties/dom-node"]
property_xml_string: Literal["http://xml.org/sax/properties/xml-string"]
property_encoding: Literal["http://www.python.org/sax/properties/encoding"]
property_interning_dict: Literal["http://www.python.org/sax/properties/interning-dict"]
all_properties: list[str]

if sys.version_info >= (3, 10):
    class LexicalHandler:
        def comment(self, content: str) -> None: ...
        def startDTD(self, name: str, public_id: str | None, system_id: str | None) -> None: ...
        def endDTD(self) -> None: ...
        def startCDATA(self) -> None: ...
        def endCDATA(self) -> None: ...
