"""
验证器测试
"""

import pytest

from nifa.utils.validators import (
    validate_id_card,
    validate_name,
    validate_phone,
    validate_org_code,
    validate_date_string,
    validate_amount,
    validate_required_fields
)
from nifa.exceptions.base import NifaValidationError


class TestValidateIdCard:
    """身份证验证测试"""
    
    def test_valid_18_digit_id_card(self):
        """测试有效的18位身份证"""
        # 这里使用一个计算正确的测试身份证号
        assert validate_id_card("11010119900101001X") is True
    
    def test_valid_15_digit_id_card(self):
        """测试有效的15位身份证"""
        assert validate_id_card("110101900101001") is True
    
    def test_empty_id_card(self):
        """测试空身份证号"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_id_card("")
        assert "身份证号码不能为空" in str(exc_info.value)
    
    def test_invalid_length_id_card(self):
        """测试无效长度的身份证号"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_id_card("1234567890")
        assert "身份证号码长度必须为15位或18位" in str(exc_info.value)
    
    def test_invalid_format_15_digit(self):
        """测试无效格式的15位身份证"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_id_card("11010190010100A")
        assert "15位身份证号码必须全为数字" in str(exc_info.value)
    
    def test_invalid_format_18_digit(self):
        """测试无效格式的18位身份证"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_id_card("11010119900101001A")
        assert "18位身份证号码格式不正确" in str(exc_info.value)
    
    def test_invalid_check_code(self):
        """测试无效校验码的18位身份证"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_id_card("110101199001010010")  # 校验码错误
        assert "身份证号码校验码不正确" in str(exc_info.value)


class TestValidateName:
    """姓名验证测试"""
    
    def test_valid_chinese_name(self):
        """测试有效的中文姓名"""
        assert validate_name("张三") is True
        assert validate_name("欧阳修") is True
    
    def test_valid_english_name(self):
        """测试有效的英文姓名"""
        assert validate_name("John Smith") is True
        assert validate_name("Mary Jane") is True
    
    def test_valid_mixed_name(self):
        """测试有效的中英文混合姓名"""
        assert validate_name("张John") is True
    
    def test_empty_name(self):
        """测试空姓名"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_name("")
        assert "姓名不能为空" in str(exc_info.value)
    
    def test_too_short_name(self):
        """测试过短的姓名"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_name("张")
        assert "姓名长度必须在2-50个字符之间" in str(exc_info.value)
    
    def test_too_long_name(self):
        """测试过长的姓名"""
        long_name = "张" * 51
        with pytest.raises(NifaValidationError) as exc_info:
            validate_name(long_name)
        assert "姓名长度必须在2-50个字符之间" in str(exc_info.value)
    
    def test_invalid_characters(self):
        """测试包含无效字符的姓名"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_name("张三123")
        assert "姓名只能包含中文、英文字母、点号和中点" in str(exc_info.value)


class TestValidatePhone:
    """手机号验证测试"""
    
    def test_valid_phone_numbers(self):
        """测试有效的手机号"""
        valid_phones = [
            "13812345678",
            "15987654321",
            "18612345678",
            "19912345678"
        ]
        for phone in valid_phones:
            assert validate_phone(phone) is True
    
    def test_empty_phone(self):
        """测试空手机号"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_phone("")
        assert "手机号码不能为空" in str(exc_info.value)
    
    def test_invalid_phone_format(self):
        """测试无效格式的手机号"""
        invalid_phones = [
            "12812345678",  # 不是1开头的有效号段
            "1381234567",   # 长度不够
            "138123456789", # 长度过长
            "13812345abc",  # 包含字母
        ]
        for phone in invalid_phones:
            with pytest.raises(NifaValidationError) as exc_info:
                validate_phone(phone)
            assert "手机号码格式不正确" in str(exc_info.value)


class TestValidateOrgCode:
    """机构代码验证测试"""
    
    def test_valid_org_codes(self):
        """测试有效的机构代码"""
        valid_codes = [
            "12345678",
            "ABCD1234",
            "91110000123456789X"
        ]
        for code in valid_codes:
            assert validate_org_code(code) is True
    
    def test_empty_org_code(self):
        """测试空机构代码"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_org_code("")
        assert "机构代码不能为空" in str(exc_info.value)
    
    def test_invalid_org_code_format(self):
        """测试无效格式的机构代码"""
        invalid_codes = [
            "1234567",      # 长度不够
            "123456789012345678901",  # 长度过长
            "1234567@",     # 包含特殊字符
        ]
        for code in invalid_codes:
            with pytest.raises(NifaValidationError) as exc_info:
                validate_org_code(code)
            assert "机构代码格式不正确" in str(exc_info.value)


class TestValidateDateString:
    """日期字符串验证测试"""
    
    def test_valid_date_strings(self):
        """测试有效的日期字符串"""
        valid_dates = [
            "2023-01-01",
            "2023-12-31",
            "2024-02-29"  # 闰年
        ]
        for date_str in valid_dates:
            assert validate_date_string(date_str) is True
    
    def test_empty_date_string(self):
        """测试空日期字符串"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_date_string("")
        assert "日期不能为空" in str(exc_info.value)
    
    def test_invalid_date_format(self):
        """测试无效格式的日期字符串"""
        invalid_dates = [
            "2023/01/01",   # 错误的分隔符
            "01-01-2023",   # 错误的顺序
            "2023-13-01",   # 无效的月份
            "2023-02-30",   # 无效的日期
            "not-a-date"    # 完全无效
        ]
        for date_str in invalid_dates:
            with pytest.raises(NifaValidationError) as exc_info:
                validate_date_string(date_str)
            assert "日期格式不正确" in str(exc_info.value)


class TestValidateAmount:
    """金额验证测试"""
    
    def test_valid_amounts(self):
        """测试有效的金额"""
        valid_amounts = [
            "100",
            "100.50",
            "0.01",
            "999999999.99"
        ]
        for amount in valid_amounts:
            assert validate_amount(amount) is True
    
    def test_empty_amount(self):
        """测试空金额"""
        with pytest.raises(NifaValidationError) as exc_info:
            validate_amount("")
        assert "金额不能为空" in str(exc_info.value)
    
    def test_invalid_amount_format(self):
        """测试无效格式的金额"""
        invalid_amounts = [
            "100.123",      # 超过2位小数
            "-100",         # 负数
            "abc",          # 非数字
            "100.50.50",    # 多个小数点
        ]
        for amount in invalid_amounts:
            with pytest.raises(NifaValidationError):
                validate_amount(amount)
    
    def test_amount_range(self):
        """测试金额范围"""
        # 测试超出范围的金额
        with pytest.raises(NifaValidationError) as exc_info:
            validate_amount("1000000000")  # 超过最大值
        assert "金额不能超过999999999.99" in str(exc_info.value)


class TestValidateRequiredFields:
    """必填字段验证测试"""
    
    def test_valid_required_fields(self):
        """测试有效的必填字段"""
        data = {
            "field1": "value1",
            "field2": "value2",
            "field3": "value3"
        }
        required_fields = ["field1", "field2"]
        assert validate_required_fields(data, required_fields) is True
    
    def test_missing_required_fields(self):
        """测试缺少必填字段"""
        data = {
            "field1": "value1"
        }
        required_fields = ["field1", "field2", "field3"]
        
        with pytest.raises(NifaValidationError) as exc_info:
            validate_required_fields(data, required_fields)
        assert "缺少必填字段" in str(exc_info.value)
        assert "field2" in str(exc_info.value)
        assert "field3" in str(exc_info.value)
    
    def test_empty_required_field_values(self):
        """测试必填字段值为空"""
        data = {
            "field1": "",
            "field2": None,
            "field3": "   "  # 只有空格
        }
        required_fields = ["field1", "field2", "field3"]
        
        with pytest.raises(NifaValidationError) as exc_info:
            validate_required_fields(data, required_fields)
        assert "缺少必填字段" in str(exc_info.value)
