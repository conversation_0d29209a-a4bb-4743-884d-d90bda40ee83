from collections.abc import Callable, Generator, Iterable
from re import <PERSON><PERSON>
from typing import Any, Literal, TypeVar, overload
from typing_extensions import TypeAlias
from xml.etree.ElementTree import Element

xpath_tokenizer_re: Pattern[str]

_Token: TypeAlias = tuple[str, str]
_Next: TypeAlias = Callable[[], _Token]
_Callback: TypeAlias = Callable[[_SelectorContext, Iterable[Element]], Generator[Element, None, None]]
_T = TypeVar("_T")

def xpath_tokenizer(pattern: str, namespaces: dict[str, str] | None = None) -> Generator[_Token, None, None]: ...
def get_parent_map(context: _SelectorContext) -> dict[Element, Element]: ...
def prepare_child(next: _Next, token: _Token) -> _Callback: ...
def prepare_star(next: _Next, token: _Token) -> _Callback: ...
def prepare_self(next: _Next, token: _Token) -> _Callback: ...
def prepare_descendant(next: _Next, token: _Token) -> _Callback | None: ...
def prepare_parent(next: _Next, token: _Token) -> _Callback: ...
def prepare_predicate(next: _Next, token: _Token) -> _Callback | None: ...

ops: dict[str, Callable[[_Next, _Token], _Callback | None]]

class _SelectorContext:
    parent_map: dict[Element, Element] | None
    root: Element
    def __init__(self, root: Element) -> None: ...

@overload
def iterfind(  # type: ignore[overload-overlap]
    elem: Element[Any], path: Literal[""], namespaces: dict[str, str] | None = None
) -> None: ...
@overload
def iterfind(elem: Element[Any], path: str, namespaces: dict[str, str] | None = None) -> Generator[Element, None, None]: ...
def find(elem: Element[Any], path: str, namespaces: dict[str, str] | None = None) -> Element | None: ...
def findall(elem: Element[Any], path: str, namespaces: dict[str, str] | None = None) -> list[Element]: ...
@overload
def findtext(elem: Element[Any], path: str, default: None = None, namespaces: dict[str, str] | None = None) -> str | None: ...
@overload
def findtext(elem: Element[Any], path: str, default: _T, namespaces: dict[str, str] | None = None) -> _T | str: ...
