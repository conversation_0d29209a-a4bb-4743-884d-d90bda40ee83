from typing import Union, Tuple, overload, Optional

Buffer = bytes|bytearray|memoryview

class ChaCha20Poly1305Cipher:
    nonce: bytes

    def __init__(self, key: <PERSON>uffer, nonce: <PERSON>uffer) -> None: ...
    def update(self, data: <PERSON>uffer) -> None: ...
    @overload
    def encrypt(self, plaintext: <PERSON>uffer) -> bytes: ...
    @overload
    def encrypt(self, plaintext: Buffer, output: Union[bytearray, memoryview]) -> None: ...
    @overload
    def decrypt(self, plaintext: Buffer) -> bytes: ...
    @overload
    def decrypt(self, plaintext: Buffer, output: Union[bytearray, memoryview]) -> None: ...
    def digest(self) -> bytes: ...
    def hexdigest(self) -> str: ...
    def verify(self, received_mac_tag: Buffer) -> None: ...
    def hexverify(self, received_mac_tag: str) -> None: ...
    def encrypt_and_digest(self, plaintext: <PERSON>uffer) -> <PERSON><PERSON>[bytes, bytes]: ...
    def decrypt_and_verify(self, ciphertext: <PERSON><PERSON><PERSON>, received_mac_tag: <PERSON>uffer) -> bytes: ...

def new(key: <PERSON><PERSON><PERSON>, nonce: Optional[Buffer] = ...) -> ChaCha20Poly1305Cipher: ...

block_size: int
key_size: int
