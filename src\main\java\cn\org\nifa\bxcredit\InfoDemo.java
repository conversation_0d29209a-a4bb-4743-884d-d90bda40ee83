package cn.org.nifa.bxcredit;

import java.util.HashMap;
import java.util.Map;

import com.sun.jersey.api.client.ClientResponse;

// [查询接口]
public class InfoDemo {
	private static Map<String, String> getHeaderMap(Map<String, String> map) {
		//
		String scode = Utils.getRandomNumber(10); //10 位随机数
		// 构造签名 用于身份验证
		String preparedSign = Config.ORGCODE
				+ scode
				+ map.get("sname")
				+ map.get("stype")
				+ map.get("sreason")
				+ map.get("sno")
				+ Config.KEY;
		String sign = Utils.sha256(preparedSign);
		// 构造 header
		Map<String, String> headerMap = new HashMap<String, String>();
		headerMap.put("sbankcode", Config.ORGCODE);
		headerMap.put("scode", scode);
		headerMap.put("sign", sign);
		return headerMap;

	}

	public static void main(String[] args) {
		// 构造 client--耗费资源 适合多次使用
		JerseyClient client = new JerseyClient(Config.INFO_URI_test);
		ClientResponse resp = null;
		// 构造 body
		Map<String, String> bodyMap = new HashMap<String, String>();
		bodyMap.put("sreason", "b");
		bodyMap.put("sname", "和小红");
		bodyMap.put("stype", "0");
		bodyMap.put("sno", "130131200505177860");
		bodyMap.put("ddate", "202409");
		System.out.println("[body]: \n" + Utils.map2Json(bodyMap));
		// 构造 header
		Map<String, String> headerMap = getHeaderMap(bodyMap);
		System.out.println("[header]: \n" + Utils.map2Json(headerMap));
		try {
			resp = client.post(headerMap, bodyMap);
		} catch (Exception e) {
			// 超时等 error
			System.out.println("[ERROR] todo");
			e.printStackTrace();
		}
		if (resp.getStatus() == 200) {
			System.out.println(resp.getEntity(String.class));
		} else {
			System.out.println("[ERROR] Status: " + resp.getStatus());
			System.out.println(resp.getEntity(String.class));
		}
	}
}